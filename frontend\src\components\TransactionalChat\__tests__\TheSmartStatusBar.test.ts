import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import { createI18n } from 'vue-i18n'
import { computed } from 'vue'
import TheSmartStatusBar from '../TheSmartStatusBar.vue'

// Mock the RTL utility - will be overridden in tests
let mockIsRtl = false
vi.mock('@/utils/rtl', () => ({
  useRtl: () => ({
    isRtl: computed(() => mockIsRtl),
    direction: computed(() => mockIsRtl ? 'rtl' : 'ltr')
  })
}))

// Mock the composables
vi.mock('@/composables/useTransactionFlowLogic', () => ({
  useTransactionFlowLogic: () => ({
    currentTransaction: computed(() => ({ id: 'test-transaction' })),
    timeLeft: computed(() => 1083),
    isTimerCritical: computed(() => false),
    isTimerExpired: computed(() => false),
    isElapsedTimer: computed(() => false)
  })
}))

vi.mock('@/composables/useTimerDisplay', () => ({
  useTimerDisplay: () => ({
    timerDisplayValue: computed(() => '18:03'),
    timerColorClass: computed(() => 'timer-normal'),
    timerLabel: computed(() => 'Timer')
  })
}))

// Mock Naive UI
vi.mock('naive-ui', () => ({
  NIcon: {
    name: 'NIcon',
    template: '<span><slot /></span>'
  },
  useMessage: () => ({
    info: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    error: vi.fn()
  })
}))

// Mock stores
const mockTransactionalChatStore = {
  currentStep: { key: 'makePayment', titleKey: 'transactionalChat.steps.makePayment' },
  currentStepIndex: 2,
  totalSteps: 6,
  timer: { remainingSeconds: 1083 },
  otherUser: { name: 'Test User' },
  transactionDetails: {
    amountToSend: 28500000,
    amountToReceive: 500,
    currencyFrom: 'IRR',
    currencyTo: 'CAD'
  },
  isUsersTurn: true,
  isStatusBarShrunk: true,
  pinnedAction: null,
  isActionCardVisible: false,
  chatSessionId: 'test-session-id'
}

const mockAuthStore = {
  user: { id: 'test-user-id' }
}

const mockPayerNegotiationStore = {
  currentNegotiation: { finalizedPayerId: 'test-user-id' }
}

// Create i18n instance
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      transactionalChat: {
        statusBar: {
          stepShort: 'Step {current}',
          clickToScrollToAction: 'Click to scroll to action'
        },
        steps: {
          makePaymentShort: 'Send {amount}',
          waitingPaymentShort: 'Waiting for {name}'
        }
      },
      common: {
        you: 'You'
      }
    },
    fa: {
      transactionalChat: {
        statusBar: {
          stepShort: 'مرحله {current}',
          clickToScrollToAction: 'برای رفتن به اقدام کلیک کنید'
        },
        steps: {
          makePaymentShort: 'ارسال {amount}',
          waitingPaymentShort: 'انتظار برای {name}'
        }
      },
      common: {
        you: 'شما'
      }
    }
  }
})

describe('TheSmartStatusBar', () => {
  let wrapper: any

  const createWrapper = (props = {}, rtlOverride?: boolean) => {
    // Set RTL state for this test
    if (rtlOverride !== undefined) {
      mockIsRtl = rtlOverride
    }

    return mount(TheSmartStatusBar, {
      props,
      global: {
        plugins: [
          i18n,
          createTestingPinia({
            initialState: {
              transactionalChat: mockTransactionalChatStore,
              auth: mockAuthStore,
              payerNegotiation: mockPayerNegotiationStore
            },
            stubActions: false
          })
        ]
      }
    })
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Timer Positioning in Shrunk Mode', () => {
    it('should position timer on the right side in LTR mode', () => {
      wrapper = createWrapper({}, false) // LTR mode

      const timer = wrapper.find('.shrunk-timer--ltr')
      expect(timer.exists()).toBe(true)

      // Check CSS classes for LTR positioning
      expect(timer.classes()).toContain('shrunk-timer--ltr')
      expect(timer.classes()).not.toContain('shrunk-timer--rtl')
    })

    it('should position timer on the left side in RTL mode', () => {
      wrapper = createWrapper({}, true) // RTL mode
      
      const timer = wrapper.find('.shrunk-timer--rtl')
      expect(timer.exists()).toBe(true)
      
      // Check CSS classes for RTL positioning
      expect(timer.classes()).toContain('shrunk-timer--rtl')
      expect(timer.classes()).not.toContain('shrunk-timer--ltr')
    })

    it('should apply correct RTL layout classes to status bar', () => {
      wrapper = createWrapper({}, true) // RTL mode

      const statusBar = wrapper.find('.smart-status-bar')
      const shrunkBar = wrapper.find('.shrunk-bar')

      expect(statusBar.classes()).toContain('rtl-layout')
      expect(shrunkBar.classes()).toContain('shrunk-bar--rtl')

      // Verify that RTL mode uses row-reverse flex direction
      const shrunkBarElement = shrunkBar.element as HTMLElement
      const computedStyle = window.getComputedStyle(shrunkBarElement)
      // Note: In test environment, we can't easily check computed styles,
      // so we just verify the class is applied
    })

    it('should display timer with correct format and bullet in RTL', () => {
      wrapper = createWrapper({}, true) // RTL mode
      
      const timer = wrapper.find('.shrunk-timer--rtl')
      expect(timer.text()).toContain('18:03 •') // RTL format: time + bullet
    })

    it('should display timer with correct format and bullet in LTR', () => {
      wrapper = createWrapper({}, false) // LTR mode
      
      const timer = wrapper.find('.shrunk-timer--ltr')
      expect(timer.text()).toContain('• 18:03') // LTR format: bullet + time
    })
  })

  describe('Content Direction in Shrunk Mode', () => {
    it('should apply RTL direction to content in RTL mode', () => {
      wrapper = createWrapper({}, true) // RTL mode
      
      const content = wrapper.find('.shrunk-content--rtl')
      expect(content.exists()).toBe(true)
      expect(content.classes()).toContain('shrunk-content--rtl')
    })

    it('should apply LTR direction to content in LTR mode', () => {
      wrapper = createWrapper({}, false) // LTR mode
      
      const content = wrapper.find('.shrunk-content--ltr')
      expect(content.exists()).toBe(true)
      expect(content.classes()).toContain('shrunk-content--ltr')
    })
  })

  describe('Responsive Behavior', () => {
    it('should show timer only when status bar is shrunk and timer is active', () => {
      // Mock store state for shrunk mode with active timer
      const storeState = {
        ...mockTransactionalChatStore,
        isStatusBarShrunk: true,
        currentStep: { key: 'makePayment' }
      }

      wrapper = createWrapper({}, false)
      
      // Timer should be visible in shrunk mode for payment steps
      const timer = wrapper.find('.shrunk-timer')
      expect(timer.exists()).toBe(true)
    })
  })
})
